<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPT Sir - AI Companion Inside Every Textbook</title>
    <link rel="stylesheet" href="gptsirSite.css">
</head>
<body>
<!-- Header -->
<header class="header">
    <div class="container">
        <nav class="nav">
            <a href="/" class="logo">
                <img src="./gptsirai-logo.png" alt="GPT Sir Logo" style="width: 150px;">
            </a>
            <ul class="nav-links">
                <li><a href="/sp/gptsir/store">Books</a></li>
                <li><a href="#benefits">Benefits</a></li>
                <li><a href="#demo">Demo</a></li>
                <li><a href="/sp/gptsir/store" class="store-btn">Store</a></li>
            </ul>
        </nav>
    </div>
</header>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <div class="hero-text">
                <h1>AI Companion Inside Every Textbook</h1>
                <a href="/sp/gptsir/store" class="hero-btn">Shop GPT Sir Books</a>
            </div>
            <div class="hero-visual">
                <div class="ai-bot">
                    <div class="bot-face">😊</div>
                </div>
                <div class="textbook"></div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="benefits" id="benefits">
    <div class="container">
        <h2>Benefits</h2>
        <div class="benefits-grid">
            <div class="benefit-card doubt-solving">
                <div class="benefit-icon">❓</div>
                <div class="benefit-text">
                    <h3>Instant Doubt Solving</h3>
                </div>
            </div>
            <div class="benefit-card ai-tutor">
                <div class="benefit-icon">🤖</div>
                <div class="benefit-text">
                    <h3>AI Tutor</h3>
                </div>
            </div>
            <div class="benefit-card step-by-step">
                <div class="benefit-icon">📋</div>
                <div class="benefit-text">
                    <h3>Step-by-Step Concept Walkthroughs</h3>
                </div>
            </div>
            <div class="benefit-card adaptive-practice">
                <div class="benefit-icon">🎯</div>
                <div class="benefit-text">
                    <h3>Interactive MCQs</h3>
                </div>
            </div>
        </div>

        <!-- Stats -->
        <div class="stats">
            <div class="stat">
                <div class="stat-number">2.3M+</div>
                <div class="stat-label">AI Interactions</div>
            </div>
            <div class="stat">
                <div class="stat-number">100+</div>
                <div class="stat-label">Books</div>
            </div>
            <div class="stat">
                <div class="stat-number">200</div>
                <div class="stat-label">schools</div>
            </div>
        </div>

        <!-- Categories -->
        <h2>Categories</h2>
        <div class="categories-grid">
            <div class="category-card ncert">
                <div class="category-book"></div>
                <div class="category-title">NCERT</div>
                <a href="#" class="view-titles">View titles</a>
            </div>
            <div class="category-card state-boards">
                <div class="category-book"></div>
                <div class="category-title">State Boards</div>
                <a href="#" class="view-titles">View titles</a>
            </div>
            <div class="category-card jee-neet">
                <div class="category-book"></div>
                <div class="category-title">JEE NEET</div>
                <a href="#" class="view-titles">View titles</a>
            </div>
        </div>

        <!-- Campus Section -->
        <div class="campus-section" id="demo">
            <div class="campus-demo">
                <div class="phone-mockup">
                    <div class="phone-screen">
                        <div class="phone-content">
                            <div class="chat-bubble">💬</div>
                        </div>
                    </div>
                </div>
                <ul class="campus-features">
                    <li>Context-aware chat interface</li>
                    <li>Tailored to board curriculum</li>
                    <li>Dashboard for progress tracking</li>
                </ul>
            </div>
            <div class="campus-cta">
                <h2>Run an AI Campus in 48 hours</h2>
                <button class="consultation-btn">Book a consultation</button>
            </div>
        </div>
    </div>
</section>

<script>
    // Add smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add hover effects for cards
    document.querySelectorAll('.benefit-card, .category-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
</script>
</body>
</html>