* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: #f8f9fa;
  line-height: 1.6;
}
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
/* Header */
.header {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}
.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  text-decoration: none;
}
.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4f46e5, #7c3aed);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}
.nav-links {
  display: flex;
  list-style: none;
  gap: 30px;
  align-items: center;
}
.nav-links a {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  transition: color 0.3s;
}
.nav-links a:hover {
  color: #4f46e5;
}
.store-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white !important;
  padding: 10px 20px;
  border-radius: 8px;
  transition: transform 0.2s;
}
.store-btn:hover {
  transform: translateY(-2px);
}
/* Hero Section */
.hero {
  background: linear-gradient(135deg, #4f46e5, #7c3aed, #06b6d4);
  color: white;
  padding: 60px 0;
  position: relative;
  overflow: hidden;
}
.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}
.hero-text h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.1;
}
.hero-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  backdrop-filter: blur(10px);
  text-decoration: none;
}
.hero-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}
.ai-bot {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.bot-face {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}
.textbook {
  width: 140px;
  height: 100px;
  background: white;
  border-radius: 10px;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}
.textbook::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 30px;
  right: 30px;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
}
.textbook::after {
  content: '';
  position: absolute;
  top: 35px;
  left: 30px;
  right: 30px;
  height: 3px;
  background: #e5e7eb;
  border-radius: 2px;
}
/* Benefits Section */
.benefits {
  padding: 80px 0;
}
.benefits h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 50px;
}
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-bottom: 60px;
}
.benefit-card {
  padding: 40px;
  border-radius: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s;
}
.benefit-card:hover {
  transform: translateY(-5px);
}
.benefit-card.doubt-solving {
  background: linear-gradient(135deg, #f59e0b, #ef4444, #8b5cf6);
}
.benefit-card.ai-tutor {
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
}
.benefit-card.step-by-step {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
}
.benefit-card.adaptive-practice {
  background: linear-gradient(135deg, #84cc16, #f59e0b);
}
.benefit-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}
.benefit-text h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 5px;
}
/* Stats */
.stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
  margin-bottom: 60px;
}
.stat {
  color: #1f2937;
}
.stat-number {
  font-size: 36px;
  font-weight: 700;
  color: #4f46e5;
}
.stat-label {
  font-size: 18px;
  color: #6b7280;
  margin-top: 5px;
}
/* Categories */
.categories h2 {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
}
.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  margin-bottom: 60px;
}
.category-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 15px;
  transition: transform 0.3s;
}
.category-card:hover {
  transform: translateY(-5px);
}
.category-card.ncert {
  background: linear-gradient(135deg, #8b5cf6, #6366f1);
  color: white;
}
.category-card.state-boards {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
  color: white;
}
.category-card.jee-neet {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}
.category-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
}
.category-book {
  width: 80px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: 0 auto 20px;
  position: relative;
}
.category-book::before {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 2px;
}
.view-titles {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
}
/* Campus Section */
.campus-demo {
  display: flex;
  align-items: center;
}
.campus-section {
  background: linear-gradient(135deg, #f59e0b, #ec4899);
  border-radius: 20px;
  padding: 60px;
  color: white;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}
.campus-features {
  list-style: none;
}
.campus-features li {
  font-size: 18px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.campus-features li::before {
  content: '•';
  color: #fbbf24;
  font-size: 24px;
}
.phone-mockup {
  width: 100px;
  height: 200px;
  background: #1f2937;
  border-radius: 10px;
  padding: 10px;
  position: relative;
  margin: 0 auto;
}
.phone-screen {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 10px;
  padding: 10px;
  position: relative;
}
.phone-content {
  height: 100%;
  background: linear-gradient(to bottom, #f3f4f6, #e5e7eb);
  border-radius: 10px;
  position: relative;
}
.chat-bubble {
  position: absolute;
  bottom: 20px;
  right: 10px;
  width: 25px;
  height: 25px;
  background: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}
.campus-cta h2 {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 30px;
}
.consultation-btn {
  background: #1d4ed8;
  color: white;
  padding: 15px 30px;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}
.consultation-btn:hover {
  background: #1e40af;
  transform: translateY(-2px);
}
/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }
  .hero-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 40px;
  }
  .hero-text h1 {
    font-size: 36px;
  }
  .benefits-grid {
    grid-template-columns: 1fr;
  }
  .categories-grid {
    grid-template-columns: 1fr;
  }
  .campus-section {
    grid-template-columns: 1fr;
    padding: 40px 20px;
  }
  .stats {
    flex-direction: column;
    gap: 30px;
  }
  .campus-demo {
    flex-direction: column;
  }
}
